import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'reactstrap';

import { IQuoteDetailResponse } from '@/apis/quotes/quotes.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';

interface Props {
    data?: IQuoteDetailResponse;
}

const FileInfo = ({ data }: Props) => {
    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {/* File items */}
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>

            <BoxAddInfo
                title='Hợp đồng'
                length={data?.contracts?.length || 0}
                content={
                    data?.contracts?.map((contract) => ({
                        title: contract.name,
                        data: [
                            {
                                label: 'Ngày tạo',
                                value: FormattedDateTimeWithFormat(
                                    contract.createdDate,
                                ),
                                boxColor: false,
                            },
                            {
                                label: 'Người tạo',
                                value: contract.creatorName,
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Cơ hội'
                length={data?.deals?.length || 0}
                content={
                    data?.deals?.map((deal) => ({
                        title: deal.title,
                        data: [
                            {
                                label: 'Doanh thu',
                                value: `${deal.amount?.toLocaleString('vi-VN')} VNĐ`,
                                boxColor: false,
                            },
                            {
                                label: 'Xác suất thành',
                                value: `${deal.probability}%`,
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />

            <BoxAddInfo
                title='Đối tác thương mại'
                length={data?.tradePartners?.length || 0}
                content={
                    data?.tradePartners?.map((partner) => ({
                        title: data?.tradePartnerName || 'Đối tác thương mại',
                        data: [
                            {
                                label: 'Email',
                                value: partner.email,
                                boxColor: false,
                            },
                            {
                                label: 'Số điện thoại',
                                value: partner.phoneNumber,
                                boxColor: false,
                            },

                            {
                                label: 'Liên hệ',
                                value: `${partner.contacts[0].fullName}${partner.contacts.length > 1 ? ` (+${partner.contacts.length - 1} khác)` : ''}`,
                                boxColor: false,
                            },
                        ],
                        // onViewQuick: () => {},
                        // onViewDetail: () => {},
                        // onDelete: () => {},
                    })) || []
                }
                // onAdd={() => {}}
            />
        </Col>
    );
};

export default FileInfo;
